//go:build integration

package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
	"github.com/jackc/pgx/v5/pgxpool"
)

type testEnvironment struct {
	dbConn      *pgxpool.Pool
	queries     *database.Queries
	authService *auth.AuthService
}

func setupTestEnvironment(dbConn *pgxpool.Pool) *testEnvironment {
	env := &testEnvironment{
		dbConn:      dbConn,
		queries:     database.New(dbConn),
		authService: auth.NewAuthService(secretKey, environment, database.New(dbConn)),
	}
	return env
}

func (env *testEnvironment) createAuthToken(t *testing.T, accountID uuid.UUID) string {
	ctx := auth.ContextWithAccountID(context.Background(), accountID)
	tokenResponse, err := env.authService.CreateAccessToken(ctx)
	if err != nil {
		t.Fatalf("Failed to create access token: %v", err)
	}
	return tokenResponse.AccessToken
}

type testSignalEndpoint struct {
	isnSlug          string
	signalTypeSlug   string
	signalTypeSemVer string
}

// createValidSignalPayload creates json valid for https://github.com/information-sharing-networks/signalsd_test_schemas/blob/main/2025.05.13/integration-test-schema.json
func createValidSignalPayload(localRef string) map[string]any {
	return map[string]any{
		"signals": []map[string]any{
			{
				"local_ref": localRef,
				"content": map[string]any{
					"test": "valid content for simple schema",
				},
			},
		},
	}
}

func createInvalidSignalPayload(localRef string) map[string]any {
	return map[string]any{
		"signals": []map[string]any{
			{
				"local_ref": localRef,
				"content": map[string]any{
					"invalid_field": "this should fail schema validation",
					// Missing required "test" field
				},
			},
		},
	}
}

// createMultipleSignalsPayload creates a payload with multiple signals
func createMultipleSignalsPayload(localRefs []string) map[string]any {
	signals := make([]map[string]any, len(localRefs))
	for i, ref := range localRefs {
		signals[i] = map[string]any{
			"local_ref": ref,
			"content": map[string]any{
				"test": fmt.Sprintf("signal content %d", i+1),
			},
		}
	}
	return map[string]any{
		"signals": signals,
	}
}

// createEmptySignalsPayload creates a payload with empty signals array
func createEmptySignalsPayload() map[string]any {
	return map[string]any{
		"signals": []map[string]any{},
	}
}

// logResponseDetails logs detailed response information for debugging failed tests
func logResponseDetails(t *testing.T, response *http.Response, testName string) {
	t.Helper()

	t.Logf("=== Response Details for %s ===", testName)
	t.Logf("Status: %d %s", response.StatusCode, response.Status)
	t.Logf("Headers: %v", response.Header)

	// Read response body for logging (this consumes the body)
	bodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		t.Logf("Failed to read response body: %v", err)
	} else {
		t.Logf("Body: %s", string(bodyBytes))
	}

	t.Logf("=== End Response Details ===")
}

// TestSingalSubmissionIntegration tests the signal submission pipeline end-to-end (starts a http server as a go routine)
func TestSignalSubmissionIntegration(t *testing.T) {

	ctx := context.Background()

	testDB := setupTestDatabase(t, ctx)

	testEnv := setupTestEnvironment(testDB)

	baseURL, stopServer := startInProcessServer(t, ctx, testEnv.dbConn, testDatabaseURL)
	defer stopServer() // Ensure server shuts down when test completes
	t.Logf("✅ Server started at %s", baseURL)

	// create test data
	t.Log("Creating test data...")

	ownerAccount := createTestAccount(t, ctx, testEnv.queries, "owner", "user", "<EMAIL>")
	adminAccount := createTestAccount(t, ctx, testEnv.queries, "admin", "user", "<EMAIL>")
	memberAccount := createTestAccount(t, ctx, testEnv.queries, "member", "user", "<EMAIL>")

	ownerISN := createTestISN(t, ctx, testEnv.queries, "owner-isn", "Owner ISN", ownerAccount.ID)
	adminISN := createTestISN(t, ctx, testEnv.queries, "admin-isn", "Admin ISN", adminAccount.ID)
	publicISN := createTestISN(t, ctx, testEnv.queries, "public-isn", "Public ISN", adminAccount.ID)

	//todo -check which of these is needed
	_ = createTestSignalType(t, ctx, testEnv.queries, ownerISN.ID, "owner ISN signal", "1.0.0")
	adminSignalType := createTestSignalType(t, ctx, testEnv.queries, adminISN.ID, "admin ISN signal", "1.0.0")
	_ = createTestSignalType(t, ctx, testEnv.queries, publicISN.ID, "public ISN signal", "1.0.0")

	grantPermission(t, ctx, testEnv.queries, adminISN.ID, memberAccount.ID, "read")

	// owner can write to all ISNs (automatically granted)
	// admin can write to the admin and public isns (automatically granted)
	// member has not been granted any write permissions
	// member has been granted read to admin
	// member can read public without auth

	// todo mixed success / failure batch
	tests := []struct {
		name            string
		accountID       uuid.UUID
		endpoint        testSignalEndpoint
		payloadFunc     func() map[string]any
		expectedStatus  int
		expectError     bool
		skipAuthToken   bool
		customAuthToken string
		expectedStored  int // Expected stored_count in response
		expectedFailed  int // Expected failed_count in response
	}{
		{
			name:      "successful_signal_submission_by_admin",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createValidSignalPayload("admin-test-001") },
			expectedStatus: http.StatusOK,
			expectError:    false,
			expectedStored: 1,
			expectedFailed: 0,
		},
		{
			name:      "successful_signal_submission_by_owner",
			accountID: ownerAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createValidSignalPayload("owner-test-001") },
			expectedStatus: http.StatusOK,
			expectError:    false,
			expectedStored: 1,
			expectedFailed: 0,
		},
		{
			name:      "unauthorized_signal_submission_by_member",
			accountID: memberAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createValidSignalPayload("member-test-001") },
			expectedStatus: http.StatusForbidden,
			expectError:    true,
			expectedStored: 0, // No response body expected for auth failures
			expectedFailed: 0,
		},
		{
			name:      "invalid_auth_token",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:     func() map[string]any { return createValidSignalPayload("invalid-auth-001") },
			expectedStatus:  http.StatusUnauthorized,
			expectError:     true,
			customAuthToken: "invalid-token",
			expectedStored:  0, // No response body expected for auth failures
			expectedFailed:  0,
		},
		{
			name:      "schema_validation_failure",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createInvalidSignalPayload("invalid-schema-001") },
			expectedStatus: http.StatusUnprocessableEntity,
			expectError:    false,
			expectedStored: 0, // Schema validation failure - no signals stored
			expectedFailed: 1, // One signal failed validation
		},
		{
			name:      "empty_signals_array",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createEmptySignalsPayload() },
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			expectedStored: 0, // No response body expected for bad request
			expectedFailed: 0,
		},
		{
			name:      "multiple_signals_batch",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc: func() map[string]any {
				return createMultipleSignalsPayload([]string{"batch-001", "batch-002", "batch-003"})
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
			expectedStored: 3,
			expectedFailed: 0,
		},
	}

	// Run table-driven tests
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var authToken string
			if tt.customAuthToken != "" {
				authToken = tt.customAuthToken
			} else if !tt.skipAuthToken {
				authToken = testEnv.createAuthToken(t, tt.accountID)
			}

			payload := tt.payloadFunc()

			response := submitSignalRequest(t, baseURL, payload, authToken, tt.endpoint)
			defer response.Body.Close()

			// Verify response status
			if response.StatusCode != tt.expectedStatus {
				logResponseDetails(t, response, tt.name)
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, response.StatusCode)
				return // Don't continue with further validation if status is wrong
			}

			if !tt.expectError || (tt.expectedStatus == http.StatusUnprocessableEntity) {
				var result map[string]any
				if err := json.NewDecoder(response.Body).Decode(&result); err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}

				// Verify summary section exists
				summary, ok := result["summary"]
				if !ok {
					t.Error("Missing summary in response")
					return
				}

				summaryMap, ok := summary.(map[string]any)
				if !ok {
					t.Error("Summary is not a map")
					return
				}

				// Verify stored_count
				var countMismatch bool

				if storedCount, ok := summaryMap["stored_count"]; ok {
					if storedCountFloat, ok := storedCount.(float64); ok {
						actualStored := int(storedCountFloat)
						if actualStored != tt.expectedStored {
							countMismatch = true
							t.Errorf("Expected stored_count %d, got %d", tt.expectedStored, actualStored)
						}
					} else {
						countMismatch = true
						t.Error("stored_count is not a number")
					}
				} else {
					countMismatch = true
					t.Error("Missing stored_count in summary")
				}

				// Verify failed_count
				if failedCount, ok := summaryMap["failed_count"]; ok {
					if failedCountFloat, ok := failedCount.(float64); ok {
						actualFailed := int(failedCountFloat)
						if actualFailed != tt.expectedFailed {
							countMismatch = true
							t.Errorf("Expected failed_count %d, got %d", tt.expectedFailed, actualFailed)
						}
					} else {
						countMismatch = true
						t.Error("failed_count is not a number")
					}
				} else {
					countMismatch = true
					t.Error("Missing failed_count in summary")
				}

				// Verify total_submitted matches stored + failed
				if totalSubmitted, ok := summaryMap["total_submitted"]; ok {
					if totalFloat, ok := totalSubmitted.(float64); ok {
						actualTotal := int(totalFloat)
						expectedTotal := tt.expectedStored + tt.expectedFailed
						if actualTotal != expectedTotal {
							countMismatch = true
							t.Errorf("Expected total_submitted %d (stored %d + failed %d), got %d",
								expectedTotal, tt.expectedStored, tt.expectedFailed, actualTotal)
						}
					}
				}

				// If there were count mismatches, show detailed response for debugging
				if countMismatch {
					// We need to recreate the response for logging since the body was already read
					t.Logf("=== Response Details for %s (Count Validation Failed) ===", tt.name)
					t.Logf("Status: %d %s", response.StatusCode, response.Status)
					t.Logf("Headers: %v", response.Header)

					// Re-marshal the parsed result to show the response body
					if responseBody, err := json.MarshalIndent(result, "", "  "); err == nil {
						t.Logf("Body: %s", string(responseBody))
					} else {
						t.Logf("Body: %+v", result)
					}
					t.Logf("=== End Response Details ===")
				}
			}
		})
	}

}

// submitSignalRequest submits a signal payload with authentication
func submitSignalRequest(t *testing.T, baseURL string, payload map[string]any, token string, endpoint testSignalEndpoint) *http.Response {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Failed to marshal payload: %v", err)
	}

	url := fmt.Sprintf("%s/api/isn/%s/signal_types/%s/v%s/signals",
		baseURL, endpoint.isnSlug, endpoint.signalTypeSlug, endpoint.signalTypeSemVer)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to submit signals: %v", err)
	}

	return resp
}

// searchPublicSignals searches for signals on public ISNs (no authentication required)
func searchPublicSignals(t *testing.T, baseURL string, endpoint testSignalEndpoint) *http.Response {
	url := fmt.Sprintf("%s/api/public/isn/%s/signal_types/%s/v%s/signals/search",
		baseURL, endpoint.isnSlug, endpoint.signalTypeSlug, endpoint.signalTypeSemVer)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to search public signals: %v", err)
	}

	return resp
}

// searchPrivateSignals searches for signals on private ISNs (authentication required)
func searchPrivateSignals(t *testing.T, baseURL string, endpoint testSignalEndpoint, token string) *http.Response {
	url := fmt.Sprintf("%s/api/isn/%s/signal_types/%s/v%s/signals/search",
		baseURL, endpoint.isnSlug, endpoint.signalTypeSlug, endpoint.signalTypeSemVer)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to search private signals: %v", err)
	}

	return resp
}

// TestEmailPrivacyProtection tests that email addresses are properly hidden in public ISN responses
// and properly shown in private ISN responses for authorized users
func TestEmailPrivacyProtection(t *testing.T) {
	ctx := context.Background()
	testDB := setupTestDatabase(t, ctx)
	testEnv := setupTestEnvironment(testDB)

	baseURL, stopServer := startInProcessServer(t, ctx, testEnv.dbConn, testDatabaseURL)
	defer stopServer()
	t.Logf("✅ Server started at %s", baseURL)

	// Create test data - we need both public and private ISNs with signals
	t.Log("Creating test data for email privacy tests...")

	ownerAccount := createTestAccount(t, ctx, testEnv.queries, "owner", "user", "<EMAIL>")
	adminAccount := createTestAccount(t, ctx, testEnv.queries, "admin", "user", "<EMAIL>")
	memberAccount := createTestAccount(t, ctx, testEnv.queries, "member", "user", "<EMAIL>")

	// Create public ISN (emails should be hidden)
	publicISN := createTestISN(t, ctx, testEnv.queries, "public-privacy-isn", "Public Privacy ISN", ownerAccount.ID)
	publicSignalType := createTestSignalType(t, ctx, testEnv.queries, publicISN.ID, "public-privacy-signal", "1.0.0")

	// Create private ISN (emails should be visible to authorized users)
	privateISN := createTestISN(t, ctx, testEnv.queries, "private-privacy-isn", "Private Privacy ISN", ownerAccount.ID)
	privateSignalType := createTestSignalType(t, ctx, testEnv.queries, privateISN.ID, "private-privacy-signal", "1.0.0")

	// Grant permissions
	grantPermission(t, ctx, testEnv.queries, privateISN.ID, adminAccount.ID, "read")
	grantPermission(t, ctx, testEnv.queries, privateISN.ID, memberAccount.ID, "write")

	// Submit signals to both ISNs
	publicEndpoint := testSignalEndpoint{
		isnSlug:          publicISN.Slug,
		signalTypeSlug:   publicSignalType.Slug,
		signalTypeSemVer: publicSignalType.SemVer,
	}

	privateEndpoint := testSignalEndpoint{
		isnSlug:          privateISN.Slug,
		signalTypeSlug:   privateSignalType.Slug,
		signalTypeSemVer: privateSignalType.SemVer,
	}

	// Submit signals from different accounts to test email visibility
	adminToken := testEnv.createAuthToken(t, adminAccount.ID)
	memberToken := testEnv.createAuthToken(t, memberAccount.ID)

	// Submit to public ISN
	publicPayload := createValidSignalPayload("public-privacy-signal-001")
	publicResponse := submitSignalRequest(t, baseURL, publicPayload, adminToken, publicEndpoint)
	publicResponse.Body.Close()
	if publicResponse.StatusCode != http.StatusOK {
		t.Fatalf("Failed to submit signal to public ISN: %d", publicResponse.StatusCode)
	}

	// Submit to private ISN
	privatePayload := createValidSignalPayload("private-privacy-signal-001")
	privateResponse := submitSignalRequest(t, baseURL, privatePayload, memberToken, privateEndpoint)
	privateResponse.Body.Close()
	if privateResponse.StatusCode != http.StatusOK {
		t.Fatalf("Failed to submit signal to private ISN: %d", privateResponse.StatusCode)
	}

	// Test cases for email privacy
	tests := []struct {
		name           string
		endpoint       testSignalEndpoint
		usePublicAPI   bool
		authToken      string
		expectEmails   bool
		expectedStatus int
		description    string
	}{
		{
			name:           "public_isn_unauthenticated_no_emails",
			endpoint:       publicEndpoint,
			usePublicAPI:   true,
			authToken:      "",
			expectEmails:   false,
			expectedStatus: http.StatusOK,
			description:    "Public ISN accessed without auth should hide emails",
		},
		{
			name:           "public_isn_authenticated_no_emails",
			endpoint:       publicEndpoint,
			usePublicAPI:   true,
			authToken:      adminToken,
			expectEmails:   false,
			expectedStatus: http.StatusOK,
			description:    "Public ISN accessed with auth should still hide emails",
		},
		{
			name:           "private_isn_authenticated_show_emails",
			endpoint:       privateEndpoint,
			usePublicAPI:   false,
			authToken:      adminToken,
			expectEmails:   true,
			expectedStatus: http.StatusOK,
			description:    "Private ISN accessed with proper auth should show emails",
		},
		{
			name:           "private_isn_unauthenticated_denied",
			endpoint:       privateEndpoint,
			usePublicAPI:   false,
			authToken:      "",
			expectEmails:   false,
			expectedStatus: http.StatusUnauthorized,
			description:    "Private ISN accessed without auth should be denied",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var response *http.Response

			if tt.usePublicAPI {
				response = searchPublicSignals(t, baseURL, tt.endpoint)
			} else {
				response = searchPrivateSignals(t, baseURL, tt.endpoint, tt.authToken)
			}
			defer response.Body.Close()

			// Verify response status
			if response.StatusCode != tt.expectedStatus {
				logResponseDetails(t, response, tt.name)
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, response.StatusCode)
				return
			}

			// Only check email privacy for successful responses
			if response.StatusCode == http.StatusOK {
				var signals []map[string]any
				if err := json.NewDecoder(response.Body).Decode(&signals); err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}

				if len(signals) == 0 {
					t.Error("Expected at least one signal in response")
					return
				}

				// Check email field in first signal
				signal := signals[0]
				email, hasEmail := signal["email"]

				if tt.expectEmails {
					if !hasEmail {
						t.Error("Expected email field to be present")
					} else if emailStr, ok := email.(string); !ok || emailStr == "" {
						t.Errorf("Expected non-empty email, got: %v", email)
					} else {
						t.Logf("✅ Email properly shown: %s", emailStr)
					}
				} else {
					if hasEmail {
						if emailStr, ok := email.(string); ok && emailStr != "" {
							t.Errorf("Expected email to be hidden, but got: %s", emailStr)
						} else {
							t.Logf("✅ Email properly hidden")
						}
					}
				}
			}
		})
	}
}

// TestCrossISNDataLeakage tests that users cannot access signals from ISNs they don't have permissions for
// This is critical for data privacy - ensures proper isolation between different ISNs
func TestCrossISNDataLeakage(t *testing.T) {
	ctx := context.Background()
	testDB := setupTestDatabase(t, ctx)
	testEnv := setupTestEnvironment(testDB)

	baseURL, stopServer := startInProcessServer(t, ctx, testEnv.dbConn, testDatabaseURL)
	defer stopServer()
	t.Logf("✅ Server started at %s", baseURL)

	// Create test data with multiple ISNs and different permission levels
	t.Log("Creating test data for cross-ISN data leakage tests...")

	// Create accounts
	ownerAccount := createTestAccount(t, ctx, testEnv.queries, "owner", "user", "<EMAIL>")
	adminAccount := createTestAccount(t, ctx, testEnv.queries, "admin", "user", "<EMAIL>")
	memberAccount := createTestAccount(t, ctx, testEnv.queries, "member", "user", "<EMAIL>")
	isolatedAccount := createTestAccount(t, ctx, testEnv.queries, "isolated", "user", "<EMAIL>")

	// Create multiple ISNs with different owners and permissions
	ownerISN := createTestISN(t, ctx, testEnv.queries, "owner-leakage-isn", "Owner Leakage ISN", ownerAccount.ID)
	ownerSignalType := createTestSignalType(t, ctx, testEnv.queries, ownerISN.ID, "owner-leakage-signal", "1.0.0")

	adminISN := createTestISN(t, ctx, testEnv.queries, "admin-leakage-isn", "Admin Leakage ISN", adminAccount.ID)
	adminSignalType := createTestSignalType(t, ctx, testEnv.queries, adminISN.ID, "admin-leakage-signal", "1.0.0")

	memberISN := createTestISN(t, ctx, testEnv.queries, "member-leakage-isn", "Member Leakage ISN", memberAccount.ID)
	memberSignalType := createTestSignalType(t, ctx, testEnv.queries, memberISN.ID, "member-leakage-signal", "1.0.0")

	// Grant specific permissions
	// admin has read access to owner's ISN
	grantPermission(t, ctx, testEnv.queries, ownerISN.ID, adminAccount.ID, "read")
	// member has write access to admin's ISN
	grantPermission(t, ctx, testEnv.queries, adminISN.ID, memberAccount.ID, "write")
	// isolated account has NO permissions to any ISN except their own (none created)

	// Create endpoints for each ISN
	ownerEndpoint := testSignalEndpoint{
		isnSlug:          ownerISN.Slug,
		signalTypeSlug:   ownerSignalType.Slug,
		signalTypeSemVer: ownerSignalType.SemVer,
	}

	adminEndpoint := testSignalEndpoint{
		isnSlug:          adminISN.Slug,
		signalTypeSlug:   adminSignalType.Slug,
		signalTypeSemVer: adminSignalType.SemVer,
	}

	memberEndpoint := testSignalEndpoint{
		isnSlug:          memberISN.Slug,
		signalTypeSlug:   memberSignalType.Slug,
		signalTypeSemVer: memberSignalType.SemVer,
	}

	// Submit signals to each ISN
	ownerToken := testEnv.createAuthToken(t, ownerAccount.ID)
	adminToken := testEnv.createAuthToken(t, adminAccount.ID)
	memberToken := testEnv.createAuthToken(t, memberAccount.ID)
	isolatedToken := testEnv.createAuthToken(t, isolatedAccount.ID)

	// Submit signals with identifiable content
	ownerPayload := createValidSignalPayload("owner-secret-data")
	ownerResponse := submitSignalRequest(t, baseURL, ownerPayload, ownerToken, ownerEndpoint)
	ownerResponse.Body.Close()
	if ownerResponse.StatusCode != http.StatusOK {
		t.Fatalf("Failed to submit signal to owner ISN: %d", ownerResponse.StatusCode)
	}

	adminPayload := createValidSignalPayload("admin-secret-data")
	adminResponse := submitSignalRequest(t, baseURL, adminPayload, adminToken, adminEndpoint)
	adminResponse.Body.Close()
	if adminResponse.StatusCode != http.StatusOK {
		t.Fatalf("Failed to submit signal to admin ISN: %d", adminResponse.StatusCode)
	}

	memberPayload := createValidSignalPayload("member-secret-data")
	memberResponse := submitSignalRequest(t, baseURL, memberPayload, memberToken, memberEndpoint)
	memberResponse.Body.Close()
	if memberResponse.StatusCode != http.StatusOK {
		t.Fatalf("Failed to submit signal to member ISN: %d", memberResponse.StatusCode)
	}

	// Test cross-ISN access attempts
	tests := []struct {
		name           string
		requesterToken string
		targetEndpoint testSignalEndpoint
		expectedStatus int
		shouldSeeData  bool
		description    string
	}{
		{
			name:           "owner_can_access_own_isn",
			requesterToken: ownerToken,
			targetEndpoint: ownerEndpoint,
			expectedStatus: http.StatusOK,
			shouldSeeData:  true,
			description:    "Owner should access their own ISN",
		},
		{
			name:           "admin_can_read_owner_isn",
			requesterToken: adminToken,
			targetEndpoint: ownerEndpoint,
			expectedStatus: http.StatusOK,
			shouldSeeData:  true,
			description:    "Admin should access owner ISN (has read permission)",
		},
		{
			name:           "member_can_access_admin_isn",
			requesterToken: memberToken,
			targetEndpoint: adminEndpoint,
			expectedStatus: http.StatusOK,
			shouldSeeData:  true,
			description:    "Member should access admin ISN (has write permission)",
		},
		{
			name:           "isolated_cannot_access_owner_isn",
			requesterToken: isolatedToken,
			targetEndpoint: ownerEndpoint,
			expectedStatus: http.StatusForbidden,
			shouldSeeData:  false,
			description:    "Isolated account should NOT access owner ISN",
		},
		{
			name:           "isolated_cannot_access_admin_isn",
			requesterToken: isolatedToken,
			targetEndpoint: adminEndpoint,
			expectedStatus: http.StatusForbidden,
			shouldSeeData:  false,
			description:    "Isolated account should NOT access admin ISN",
		},
		{
			name:           "isolated_cannot_access_member_isn",
			requesterToken: isolatedToken,
			targetEndpoint: memberEndpoint,
			expectedStatus: http.StatusForbidden,
			shouldSeeData:  false,
			description:    "Isolated account should NOT access member ISN",
		},
		{
			name:           "member_cannot_access_owner_isn",
			requesterToken: memberToken,
			targetEndpoint: ownerEndpoint,
			expectedStatus: http.StatusForbidden,
			shouldSeeData:  false,
			description:    "Member should NOT access owner ISN (no permission)",
		},
		{
			name:           "admin_cannot_access_member_isn",
			requesterToken: adminToken,
			targetEndpoint: memberEndpoint,
			expectedStatus: http.StatusForbidden,
			shouldSeeData:  false,
			description:    "Admin should NOT access member ISN (no permission)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			response := searchPrivateSignals(t, baseURL, tt.targetEndpoint, tt.requesterToken)
			defer response.Body.Close()

			// Verify response status
			if response.StatusCode != tt.expectedStatus {
				logResponseDetails(t, response, tt.name)
				t.Errorf("Expected status %d, got %d. %s", tt.expectedStatus, response.StatusCode, tt.description)
				return
			}

			// For successful responses, verify data access
			if response.StatusCode == http.StatusOK && tt.shouldSeeData {
				var signals []map[string]any
				if err := json.NewDecoder(response.Body).Decode(&signals); err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}

				if len(signals) == 0 {
					t.Error("Expected to see signals but got empty response")
				} else {
					t.Logf("✅ Authorized access successful - found %d signals", len(signals))
				}
			}

			// For forbidden responses, ensure no data is leaked
			if response.StatusCode == http.StatusForbidden {
				t.Logf("✅ Unauthorized access properly blocked")
			}
		})
	}
}
