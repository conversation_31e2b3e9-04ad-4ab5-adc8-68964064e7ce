//go:build integration

package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
	"github.com/jackc/pgx/v5/pgxpool"
)

type testEnvironment struct {
	dbConn      *pgxpool.Pool
	queries     *database.Queries
	authService *auth.AuthService
}

func setupTestEnvironment(dbConn *pgxpool.Pool) *testEnvironment {
	env := &testEnvironment{
		dbConn:      dbConn,
		queries:     database.New(dbConn),
		authService: auth.NewAuthService(secretKey, environment, database.New(dbConn)),
	}
	return env
}

// createAuthToken generates an auth token for the given account ID
func (env *testEnvironment) createAuthToken(t *testing.T, accountID uuid.UUID) string {
	ctx := auth.ContextWithAccountID(context.Background(), accountID)
	tokenResponse, err := env.authService.CreateAccessToken(ctx)
	if err != nil {
		t.Fatalf("Failed to create access token: %v", err)
	}
	return tokenResponse.AccessToken
}

type testSignalEndpoint struct {
	isnSlug          string
	signalTypeSlug   string
	signalTypeSemVer string
}

// createValidSignalPayload creates a valid signal payload for testing
func createValidSignalPayload(localRef string) map[string]any {
	return map[string]any{
		"signals": []map[string]any{
			{
				"local_ref": localRef,
				"content": map[string]any{
					"test": "valid content for simple schema",
				},
			},
		},
	}
}

// createInvalidSignalPayload creates an invalid signal payload for testing schema validation
func createInvalidSignalPayload(localRef string) map[string]any {
	return map[string]any{
		"signals": []map[string]any{
			{
				"local_ref": localRef,
				"content": map[string]any{
					"invalid_field": "this should fail schema validation",
					// Missing required "test" field
				},
			},
		},
	}
}

// createMultipleSignalsPayload creates a payload with multiple signals
func createMultipleSignalsPayload(localRefs []string) map[string]any {
	signals := make([]map[string]any, len(localRefs))
	for i, ref := range localRefs {
		signals[i] = map[string]any{
			"local_ref": ref,
			"content": map[string]any{
				"test": fmt.Sprintf("signal content %d", i+1),
			},
		}
	}
	return map[string]any{
		"signals": signals,
	}
}

// createEmptySignalsPayload creates a payload with empty signals array
func createEmptySignalsPayload() map[string]any {
	return map[string]any{
		"signals": []map[string]any{},
	}
}

// logResponseDetails logs detailed response information for debugging failed tests
func logResponseDetails(t *testing.T, response *http.Response, testName string) {
	t.Helper()

	t.Logf("=== Response Details for %s ===", testName)
	t.Logf("Status: %d %s", response.StatusCode, response.Status)
	t.Logf("Headers: %v", response.Header)

	// Read response body for logging (this consumes the body)
	bodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		t.Logf("Failed to read response body: %v", err)
	} else {
		t.Logf("Body: %s", string(bodyBytes))
	}

	t.Logf("=== End Response Details ===")
}

// TestSingalSubmissionIntegration tests the signal submission pipeline end-to-end (starts a http server as a go routine)
func TestSignalSubmissionIntegration(t *testing.T) {

	ctx := context.Background()

	testDB := setupTestDatabase(t, ctx)

	testEnv := setupTestEnvironment(testDB)

	baseURL, stopServer := startInProcessServer(t, ctx, testEnv.dbConn, testDatabaseURL)
	defer stopServer() // Ensure server shuts down when test completes
	t.Logf("✅ Server started at %s", baseURL)

	// create test data
	t.Log("Creating test data...")

	ownerAccount := createTestAccount(t, ctx, testEnv.queries, "owner", "user", "<EMAIL>")
	adminAccount := createTestAccount(t, ctx, testEnv.queries, "admin", "user", "<EMAIL>")
	memberAccount := createTestAccount(t, ctx, testEnv.queries, "member", "user", "<EMAIL>")

	ownerISN := createTestISN(t, ctx, testEnv.queries, "owner-isn", "Owner ISN", ownerAccount.ID)
	adminISN := createTestISN(t, ctx, testEnv.queries, "admin-isn", "Admin ISN", adminAccount.ID)
	publicISN := createTestISN(t, ctx, testEnv.queries, "public-isn", "Public ISN", adminAccount.ID)

	_ = createTestSignalType(t, ctx, testEnv.queries, ownerISN.ID, "owner ISN signal", "1.0.0")
	adminSignalType := createTestSignalType(t, ctx, testEnv.queries, adminISN.ID, "admin ISN signal", "1.0.0")
	_ = createTestSignalType(t, ctx, testEnv.queries, publicISN.ID, "public ISN signal", "1.0.0")

	grantPermission(t, ctx, testEnv.queries, adminISN.ID, memberAccount.ID, "read")

	// owner can write to all ISNs (automatically granted)
	// admin can write to the admin and public isns (automatically granted)
	// member has not been granted any write permissions
	// member has been granted read to admin
	// member can read public without auth

	// Define test cases using table-driven approach
	tests := []struct {
		name            string
		accountID       uuid.UUID
		endpoint        testSignalEndpoint
		payloadFunc     func() map[string]any
		expectedStatus  int
		expectError     bool
		skipAuthToken   bool
		customAuthToken string
		expectedStored  int // Expected stored_count in response
		expectedFailed  int // Expected failed_count in response
	}{
		{
			name:      "successful_signal_submission_by_admin",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createValidSignalPayload("admin-test-001") },
			expectedStatus: http.StatusOK,
			expectError:    false,
			expectedStored: 1,
			expectedFailed: 0,
		},
		{
			name:      "successful_signal_submission_by_owner",
			accountID: ownerAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createValidSignalPayload("owner-test-001") },
			expectedStatus: http.StatusOK,
			expectError:    false,
			expectedStored: 1,
			expectedFailed: 0,
		},
		{
			name:      "unauthorized_signal_submission_by_member",
			accountID: memberAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createValidSignalPayload("member-test-001") },
			expectedStatus: http.StatusForbidden,
			expectError:    true,
			expectedStored: 0, // No response body expected for auth failures
			expectedFailed: 0,
		},
		{
			name:      "invalid_auth_token",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:     func() map[string]any { return createValidSignalPayload("invalid-auth-001") },
			expectedStatus:  http.StatusUnauthorized,
			expectError:     true,
			customAuthToken: "invalid-token",
			expectedStored:  0, // No response body expected for auth failures
			expectedFailed:  0,
		},
		{
			name:      "schema_validation_failure",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createInvalidSignalPayload("invalid-schema-001") },
			expectedStatus: http.StatusUnprocessableEntity,
			expectError:    false,
			expectedStored: 0, // Schema validation failure - no signals stored
			expectedFailed: 1, // One signal failed validation
		},
		{
			name:      "empty_signals_array",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc:    func() map[string]any { return createEmptySignalsPayload() },
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
			expectedStored: 0, // No response body expected for bad request
			expectedFailed: 0,
		},
		{
			name:      "multiple_signals_batch",
			accountID: adminAccount.ID,
			endpoint: testSignalEndpoint{
				isnSlug:          adminISN.Slug,
				signalTypeSlug:   adminSignalType.Slug,
				signalTypeSemVer: adminSignalType.SemVer,
			},
			payloadFunc: func() map[string]any {
				return createMultipleSignalsPayload([]string{"batch-001", "batch-002", "batch-003"})
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
			expectedStored: 3, // All 3 signals should be stored successfully
			expectedFailed: 0,
		},
	}

	// Run table-driven tests
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Generate auth token
			var authToken string
			if tt.customAuthToken != "" {
				authToken = tt.customAuthToken
			} else if !tt.skipAuthToken {
				authToken = testEnv.createAuthToken(t, tt.accountID)
			}

			// Generate payload
			payload := tt.payloadFunc()

			// Submit request
			response := submitSignalRequest(t, baseURL, payload, authToken, tt.endpoint)
			defer response.Body.Close()

			// Verify response status
			if response.StatusCode != tt.expectedStatus {
				// Log detailed response information for debugging
				logResponseDetails(t, response, tt.name)
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, response.StatusCode)
				return // Don't continue with further validation if status is wrong
			}

			// Additional verification for cases that return JSON responses
			if !tt.expectError || (tt.expectedStatus == http.StatusUnprocessableEntity) {
				// Parse response body to verify structure and counts
				var result map[string]any
				if err := json.NewDecoder(response.Body).Decode(&result); err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}

				// Verify summary section exists
				summary, ok := result["summary"]
				if !ok {
					t.Error("Missing summary in response")
					return
				}

				summaryMap, ok := summary.(map[string]any)
				if !ok {
					t.Error("Summary is not a map")
					return
				}

				// Track if we need to show response details due to count mismatches
				var countMismatch bool

				// Verify stored_count
				if storedCount, ok := summaryMap["stored_count"]; ok {
					if storedCountFloat, ok := storedCount.(float64); ok {
						actualStored := int(storedCountFloat)
						if actualStored != tt.expectedStored {
							countMismatch = true
							t.Errorf("Expected stored_count %d, got %d", tt.expectedStored, actualStored)
						}
					} else {
						countMismatch = true
						t.Error("stored_count is not a number")
					}
				} else {
					countMismatch = true
					t.Error("Missing stored_count in summary")
				}

				// Verify failed_count
				if failedCount, ok := summaryMap["failed_count"]; ok {
					if failedCountFloat, ok := failedCount.(float64); ok {
						actualFailed := int(failedCountFloat)
						if actualFailed != tt.expectedFailed {
							countMismatch = true
							t.Errorf("Expected failed_count %d, got %d", tt.expectedFailed, actualFailed)
						}
					} else {
						countMismatch = true
						t.Error("failed_count is not a number")
					}
				} else {
					countMismatch = true
					t.Error("Missing failed_count in summary")
				}

				// Verify total_submitted matches stored + failed
				if totalSubmitted, ok := summaryMap["total_submitted"]; ok {
					if totalFloat, ok := totalSubmitted.(float64); ok {
						actualTotal := int(totalFloat)
						expectedTotal := tt.expectedStored + tt.expectedFailed
						if actualTotal != expectedTotal {
							countMismatch = true
							t.Errorf("Expected total_submitted %d (stored %d + failed %d), got %d",
								expectedTotal, tt.expectedStored, tt.expectedFailed, actualTotal)
						}
					}
				}

				// If there were count mismatches, show detailed response for debugging
				if countMismatch {
					// We need to recreate the response for logging since the body was already read
					t.Logf("=== Response Details for %s (Count Validation Failed) ===", tt.name)
					t.Logf("Status: %d %s", response.StatusCode, response.Status)
					t.Logf("Headers: %v", response.Header)

					// Re-marshal the parsed result to show the response body
					if responseBody, err := json.MarshalIndent(result, "", "  "); err == nil {
						t.Logf("Body: %s", string(responseBody))
					} else {
						t.Logf("Body: %+v", result)
					}
					t.Logf("=== End Response Details ===")
				}
			}
		})
	}

}

// submitSignalRequest submits a signal payload with authentication
func submitSignalRequest(t *testing.T, baseURL string, payload map[string]any, token string, endpoint testSignalEndpoint) *http.Response {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Failed to marshal payload: %v", err)
	}

	url := fmt.Sprintf("%s/api/isn/%s/signal_types/%s/v%s/signals",
		baseURL, endpoint.isnSlug, endpoint.signalTypeSlug, endpoint.signalTypeSemVer)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to submit signals: %v", err)
	}

	return resp
}
