//go:build integration

package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
	"github.com/jackc/pgx/v5/pgxpool"
)

type testEnvironment struct {
	dbConn      *pgxpool.Pool
	queries     *database.Queries
	authService *auth.AuthService
}

func setupTestEnvironment(dbConn *pgxpool.Pool) *testEnvironment {
	env := &testEnvironment{
		dbConn:      dbConn,
		queries:     database.New(dbConn),
		authService: auth.NewAuthService(secretKey, environment, database.New(dbConn)),
	}
	return env
}

// Helper methods for creating test data on demand
func (env *testEnvironment) createTestAccount(t *testing.T, ctx context.Context, name, accountType, email string) database.GetAccountByIDRow {
	return createTestAccount(t, ctx, env.queries, name, accountType, email)
}

func (env *testEnvironment) createTestISN(t *testing.T, ctx context.Context, slug, name string, ownerID uuid.UUID) database.Isn {
	return createTestISN(t, ctx, env.queries, slug, name, ownerID)
}

func (env *testEnvironment) createTestSignalType(t *testing.T, ctx context.Context, isnID uuid.UUID, title, semVer string) database.SignalType {
	return createTestSignalType(t, ctx, env.queries, isnID, title, semVer)
}

func (env *testEnvironment) createAuthToken(t *testing.T, accountID uuid.UUID) string {
	ctx := auth.ContextWithAccountID(context.Background(), accountID)
	tokenResponse, err := env.authService.CreateAccessToken(ctx)
	if err != nil {
		t.Fatalf("Failed to create access token: %v", err)
	}
	return tokenResponse.AccessToken
}

func (env *testEnvironment) grantPermission(t *testing.T, ctx context.Context, isnID, accountID uuid.UUID, permission string) {
	grantPermission(t, ctx, env.queries, isnID, accountID, permission)
}

type testSignalEndpoint struct {
	isnSlug          string
	signalTypeSlug   string
	signalTypeSemVer string
}

func TestSignalSubmissionHTTP(t *testing.T) {

	ctx := context.Background()

	testDB := setupTestDatabase(t, ctx)

	testEnv := setupTestEnvironment(testDB)

	baseURL, stopServer := startInProcessServer(t, ctx, testEnv.dbConn, testDatabaseURL)
	defer stopServer() // Ensure server shuts down when test completes
	t.Logf("✅ Server started at %s", baseURL)

	// create test data
	t.Log("Creating test data...")

	ownerAccount := createTestAccount(t, ctx, testEnv.queries, "owner", "user", "<EMAIL>")
	adminAccount := createTestAccount(t, ctx, testEnv.queries, "admin", "user", "<EMAIL>")
	memberAccount := createTestAccount(t, ctx, testEnv.queries, "member", "user", "<EMAIL>")

	ownerISN := createTestISN(t, ctx, testEnv.queries, "owner-isn", "Owner ISN", ownerAccount.ID)
	adminISN := createTestISN(t, ctx, testEnv.queries, "admin-isn", "Admin ISN", adminAccount.ID)
	publicISN := createTestISN(t, ctx, testEnv.queries, "public-isn", "Public ISN", adminAccount.ID)

	_ = createTestSignalType(t, ctx, testEnv.queries, ownerISN.ID, "owner ISN signal", "1.0.0")
	adminSignalType := createTestSignalType(t, ctx, testEnv.queries, adminISN.ID, "admin ISN signal", "1.0.0")
	_ = createTestSignalType(t, ctx, testEnv.queries, publicISN.ID, "public ISN signal", "1.0.0")

	grantPermission(t, ctx, testEnv.queries, adminISN.ID, memberAccount.ID, "read")

	// owner can write to all ISNs (automatically granted)
	// admin can write to the admin and public isns (automatically granted)
	// member has not been granted any write permissions
	// member has been granted read to admin
	// member can read public without auth

	// Create test signal payload matching the simple integration test schema
	signalPayload := map[string]any{
		"signals": []map[string]any{
			{
				"local_ref": "http-test-signal-001",
				"content": map[string]any{
					"test": "valid content for simple schema",
				},
			},
		},
	}

	t.Run("Admin successful signal submission to AdminISN", func(t *testing.T) {

		endpoint := testSignalEndpoint{
			isnSlug:          adminISN.Slug,
			signalTypeSlug:   adminSignalType.Slug,
			signalTypeSemVer: adminSignalType.SemVer,
		}

		authToken := testEnv.createAuthToken(t, adminAccount.ID)

		response := submitSignalRequest(t, baseURL, signalPayload, authToken, endpoint)
		defer response.Body.Close()

		// Verify response
		if response.StatusCode != http.StatusOK {
			errorBody, _ := io.ReadAll(response.Body)
			t.Errorf("Expected status 200, got %d. Response body: %s", response.StatusCode, string(errorBody))
		}

		/*
			// Parse response body
			var result map[string]any
			if err := json.NewDecoder(response.Body).Decode(&result); err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}

			// Verify response structure
			if summary, ok := result["summary"].(map[string]any); ok {
				if totalSubmitted, ok := summary["total_submitted"].(float64); ok {
					if totalSubmitted != 1 {
						t.Errorf("Expected 1 signal submitted, got %v", totalSubmitted)
					}
				} else {
					t.Error("Missing total_submitted in summary")
				}
			} else {
				t.Error("Missing summary in response")
			}

			// Verify database persistence
			testEnv.verifySignalInDatabase(t, "http-test-signal-001")
		*/
	})

}

// submitSignalRequest submits a signal payload with authentication
func submitSignalRequest(t *testing.T, baseURL string, payload map[string]any, token string, endpoint testSignalEndpoint) *http.Response {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Failed to marshal payload: %v", err)
	}

	url := fmt.Sprintf("%s/api/isn/%s/signal_types/%s/v%s/signals",
		baseURL, endpoint.isnSlug, endpoint.signalTypeSlug, endpoint.signalTypeSemVer)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to submit signals: %v", err)
	}

	return resp
}
