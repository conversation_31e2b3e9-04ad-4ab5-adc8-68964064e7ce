1. Database - when in running local integration test the standard dev docker db must be running
`docker compose up db`

the test will create a temporary databse to run the tests.

dataSetup - creates a simple admin data set for the tests

auth_test.go - tests the auth system (this using the dataSetup data)
http_test.go - tests the http handlers (this starts a server and runs http requests against it)
smoke_test.go - todo, checks command line params and that the app actually runs.

