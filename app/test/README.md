# Signalsd Testing Strategy & Documentation

## Overview

This testing suite provides comprehensive coverage of the Signalsd application's core functionality, with particular emphasis on data integrity, privacy protection, and security. The tests are designed to ensure that sensitive signal data is handled correctly and that privacy controls work as intended.

## Test Architecture

### Integration Tests (`app/test/integration/`)

The integration test suite follows Go best practices and provides end-to-end testing of the application:

**Core Test Files:**
- `auth_test.go` - Authentication and authorization system testing
- `http_test.go` - HTTP API endpoint testing with full request/response validation
- `database.go` - Shared database test helpers and data creation utilities
- `setup.go` - Test environment setup and server lifecycle management

### Unit Tests (Distributed)

Strategic unit tests are located alongside the code they test:
- `app/internal/server/utils/utils_test.go` - URL validation and SSRF protection
- `app/internal/server/request_limits_test.go` - Rate limiting and request size controls

## Test Coverage & Focus Areas

### 1. Authentication & Authorization (`auth_test.go`)

**What it tests:**
- JWT token generation and validation
- Role-based access control (owner, admin, member)
- ISN permission enforcement (read/write)
- Service account vs user account handling
- Token expiration and security claims

**Privacy & Security Coverage:**
- ✅ Ensures users can only access ISNs they have permissions for
- ✅ Validates JWT tokens contain correct permission scopes
- ✅ Tests that unauthorized access attempts are properly rejected
- ✅ Verifies service account batch isolation

### 2. HTTP API Testing (`http_test.go`)

**What it tests:**
- Signal submission pipeline end-to-end
- Schema validation and data integrity (note includes test schmea retrieval from github)
- Response structure and count validation
- Authentication failures and authorization errors
- Batch processing with mixed success/failure scenarios

**Key Features:**
- **Table-driven tests** for comprehensive scenario coverage
- **Automatic response logging** when tests fail for easy debugging
- **Count validation** ensures exact number of signals stored vs failed
- **Suppressed HTTP logs** during normal operation, detailed on failure

**Privacy & Security Coverage:**
- ✅ Tests that unauthorized users cannot submit signals
- ✅ Validates schema enforcement prevents malformed data storage
- ✅ Ensures proper error handling without data leakage
- ✅ Tests authentication token validation

### 3. Security Controls (Unit Tests)

**URL Validation (`utils_test.go`):**
- ✅ SSRF protection - only allows GitHub URLs
- ✅ File extension validation for schemas and documentation
- ✅ HTTPS enforcement

**Request Limits (`request_limits_test.go`):**
- ✅ Rate limiting functionality
- ✅ Request size limits (5MB for signals, 64KB for API)
- ✅ Proper error responses for oversized requests

## Privacy & Data Protection Analysis

### Strong Privacy Controls ✅

1. **Email Address Protection:**
   - Public ISN responses automatically hide email addresses (`Email: ""`)
   - Only authenticated users with proper permissions see creator emails

2. **ISN Visibility Controls:**
   - Public ISNs: Unauthenticated read access allowed
   - Private ISNs: Require authentication and explicit permissions
   - Public ISN cache prevents unauthorized access attempts

3. **Permission-Based Access:**
   - Granular read/write permissions per ISN
   - Owner/admin role hierarchy properly enforced
   - Service account isolation with batch-specific permissions

4. **Authentication Security:**
   - Bcrypt password hashing (cost 12)
   - JWT tokens with proper expiration (30 minutes)
   - Secure token generation using crypto/rand

### Areas Requiring Attention ⚠️

1. **Missing Privacy Tests:**
   - **No explicit tests for email address hiding** in public ISN responses
   - **No tests for cross-ISN data leakage** prevention
   - **No tests for withdrawn signal privacy** (ensuring withdrawn signals don't leak data)

2. **Potential Data Exposure Risks:**
   - **Signal content validation** - tests don't verify that invalid signals don't expose sensitive data in error messages
   - **Correlation ID handling** - no tests ensure correlation IDs don't leak across permission boundaries
   - **Database transaction isolation** - no tests verify that failed transactions don't leave partial data exposed

3. **Missing Security Tests:**
   - **No CORS testing** - critical for preventing unauthorized cross-origin access
   - **No rate limiting integration tests** - only unit tests exist
   - **No public ISN cache poisoning tests** - could allow unauthorized access

## Test Execution

### Prerequisites
```bash
# Start the development database
docker compose up db
```

### Running Tests
```bash
# Run integration tests
go test -tags=integration ./app/test/integration/

# Run unit tests
go test ./app/internal/server/utils/
go test ./app/internal/server/
```

### Test Environment
- Tests create temporary databases (`tmp_signalsd_integration_test`)
- HTTP server runs on random available ports
- Clean teardown ensures no test pollution
- Suppressed HTTP request logs for clean output

## Rationale for Current Approach

### Why Integration Tests Are Primary Focus

1. **Data Flow Validation:** Signal processing involves complex interactions between authentication, validation, database transactions, and response formatting
2. **Privacy Enforcement:** Many privacy controls span multiple layers (middleware, handlers, database queries)
3. **Real-world Scenarios:** Integration tests catch issues that unit tests miss, especially around permission boundaries

### Why Table-Driven HTTP Tests

1. **Comprehensive Coverage:** Easy to add new test scenarios without code duplication
2. **Consistent Validation:** All tests use the same validation logic for responses
3. **Clear Expectations:** Each test case explicitly states expected outcomes
4. **Debugging Support:** Automatic detailed logging when tests fail

### Why Minimal Unit Tests

1. **Strategic Focus:** Unit tests target specific security-critical functions (SSRF protection, rate limiting)
2. **Integration Coverage:** Most business logic is better tested through integration tests
3. **Maintenance Efficiency:** Fewer tests to maintain while covering critical paths

## Recommendations for Enhanced Privacy Testing

### High Priority Additions

1. **Email Privacy Tests:**
```go
func TestPublicISNEmailPrivacy(t *testing.T) {
    // Verify public ISN responses never include email addresses
    // Test both authenticated and unauthenticated requests
}
```

2. **Cross-ISN Data Leakage Tests:**
```go
func TestCrossISNDataIsolation(t *testing.T) {
    // Verify users cannot access signals from ISNs they don't have permissions for
    // Test correlation ID boundaries
}
```

3. **CORS Security Tests:**
```go
func TestCORSPolicyEnforcement(t *testing.T) {
    // Verify CORS headers prevent unauthorized cross-origin access
    // Test both public and protected endpoints
}
```

### Medium Priority Additions

1. **Withdrawn Signal Privacy Tests**
2. **Error Message Data Leakage Tests**
3. **Public ISN Cache Security Tests**
4. **Rate Limiting Integration Tests**

## Conclusion

The current test suite provides a solid foundation for ensuring data integrity and basic privacy controls. The integration-focused approach effectively validates the complex interactions in signal processing. However, **explicit privacy testing should be added** to ensure that sensitive data (particularly email addresses and cross-ISN signal content) is properly protected in all scenarios.

The table-driven HTTP tests with automatic response validation provide excellent debugging capabilities and comprehensive scenario coverage, making this a maintainable and effective testing strategy for a privacy-sensitive application.